<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⊙</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{ active: activeTab === 'info' }" @click="switchTab('info')">基本信息</view>
      <view class="tab-item" :class="{ active: activeTab === 'delivery' }" @click="switchTab('delivery')">交付信息</view>
    </view>

    <!-- 基本信息内容 -->
    <view class="content" v-if="activeTab === 'info'">
      <view class="info-container">
        <view class="info-item">
          <text class="info-label">订单号</text>
          <text class="info-value">{{ orderDetail.orderNo || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">发布时间</text>
          <text class="info-value">{{ orderDetail.createTime || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">辅导项目</text>
          <text class="info-value">{{ constants.TUTORING_ITEM_TEXT[orderDetail.tutoringItem] || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习阶段</text>
          <text class="info-value">{{ constants.STUDY_STAGE_TEXT[orderDetail.studyStage] || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习位置</text>
          <text class="info-value">{{ constants.STUDY_PLACE_TEXT[orderDetail.studyPlace] || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">学习专业</text>
          <text class="info-value">{{ orderDetail.major || '' }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">预算区间</text>
          <text class="info-value">{{ orderDetail.budgetMin || '-' }}-{{ orderDetail.budgetMax || '-' }}元</text>
        </view>

        <view class="info-section">
          <text class="section-title">需求描述</text>
          <view class="description-text">
            {{ orderDetail.requirementText || '' }}
          </view>
        </view>

        <view class="info-section">
          <text class="section-title">老师要求</text>
          <view class="teacher-requirements">
            <view class="requirement-item">
              <text class="requirement-label">老师情况</text>
              <text class="requirement-tag">{{ constants.TEACHER_LEVEL_TEXT[orderDetail.teacherLevel] }}</text>
            </view>
            <view class="requirement-item">
              <text class="requirement-label">是否留学</text>
              <text class="requirement-tag no-requirement">{{ constants.TEACHER_ABROAD_TEXT[orderDetail.teacherAbroad] }}</text>
            </view>
            <view class="teacher-description">
              {{ orderDetail.comment }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 交付信息内容 -->
    <view class="content" v-if="activeTab === 'delivery'">
      <view class="delivery-info">
        <!-- 辅导老师信息 -->
        <view class="teacher-section">
          <text class="teacher-label">辅导老师</text>
          <text class="teacher-link" @click="viewTeacher">{{ deliveryStageInfo.teacherName }} ></text>
        </view>

        <!-- 交付阶段表格 -->
        <view class="delivery-table">
          <view class="table-header">
            <text class="header-cell stage">交付阶段</text>
            <text class="header-cell amount">阶段费用</text>
            <text class="header-cell status">阶段状态</text>
            <text class="header-cell action">操作</text>
          </view>

          <view class="table-row" v-for="(stage, index) in deliveryStageInfo.deliveryInfoList" :key="index">
            <text class="table-cell stage">第{{ stage.deliveryStage }}阶段</text>
            <text class="table-cell amount">{{ stage.deliveryAmount }}</text>
            <text class="table-cell status" :class="getStageStatusClass(stage.stageStatus)">{{ constants.DELIVERY_STAGE_STATUS_TEXT[stage.stageStatus] }}</text>
            <view class="table-cell action">
              <button
                  v-if="stage.stageStatus === constants.DELIVERY_STAGE_STATUS.PAYED_WAIT_DELIVER"
                  class="confirm-btn"
                  @click="confirmStage(index)"
              >
                确认验收
              </button>

              <button
                  v-if="stage.stageStatus === constants.DELIVERY_STAGE_STATUS.WAIT_PAY"
                  class="pay-btn"
                  @click="payStage(index)"
              >
                去支付
              </button>
            </view>
          </view>

          <view class="total-row">
            <text class="total-label">共{{ deliveryStageInfo.totalDeliveryStage }}阶段</text>
            <text class="total-amount">{{ deliveryStageInfo.totalDeliveryAmount }}</text>
            <text class="total-status">{{ constants.ORDER_DELIVER_STATUS_TEXT[deliveryStageInfo.totalDeliveryStatus] }}</text>
            <text class="total-action"></text>
          </view>
        </view>

        <!-- 注释说明 -->
        <view class="notes-section">
          <text class="notes-title">注释：</text>
          <text class="notes-item">1. 每个阶段需要先支付，老师收到支付信息后才会开始作业。</text>
          <text class="notes-item">2. 老师提交阶段支付后，请先确认是否达到约定的标准，达标后再点击确认验收。</text>
          <text class="notes-item">3. 所有阶段都是已验收后辅导老师才能提现。</text>
          <text class="notes-item">4. 如果和辅导老师之前差生分歧可以找客服进行协商。</text>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <button class="terminate-btn" @click="terminateOrder">终止辅导</button>
    </view>

    <!-- 终止辅导确认弹窗 -->
    <view class="terminate-modal" v-if="showTerminateModal" @click="closeTerminateModal">
      <view class="modal-content" @click.stop>
        <text class="modal-title">终止辅导相当于完成，变为待评价</text>
        <view class="modal-actions">
          <button class="modal-btn cancel-btn" @click="closeTerminateModal">取消</button>
          <button class="modal-btn confirm-btn" @click="confirmTerminate">确定</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {constants, deliveryInfoApi, orderApi} from '../../../api'
import {handleApiResponse} from "../../../utils/apiHelper";

const orderId = ref('')
const activeTab = ref('info') // 当前激活的标签页
const showTerminateModal = ref(false) // 控制终止辅导弹窗显示

const loading = ref(false)
const orderDetail = ref({})

// 交付阶段数据
const deliveryStages = ref([])

// 交付阶段数据
const deliveryStageInfo = ref({})

const goBack = () => {
  uni.navigateBack()
}

const switchTab = (tab) => {
  activeTab.value = tab
}

// 查看老师详情
const viewTeacher = () => {
  uni.navigateTo({
    url: '/uniapp/pages/student/TeacherDetail?teacherId=' + deliveryStageInfo.value.teacherId,
  })
}

// 确认验收
const confirmStage = (index) => {
  uni.showModal({
    title: '确认验收',
    content: '确定要验收该阶段的交付内容吗？',
    success: async (res) => {
      if (res.confirm) {
        const params = { orderId: orderId.value, deliveryStage: deliveryStageInfo.value.deliveryInfoList[index].deliveryStage }

        const result = await handleApiResponse(
            deliveryInfoApi.confirm(params),
            {
              showLoading: true,
            }
        )

        if (result.success) {
          // 更新页面数据状态
          deliveryStageInfo.value.deliveryInfoList[index].stageStatus = constants.DELIVERY_STAGE_STATUS.ACCEPTED
          deliveryStageInfo.value.deliveryInfoList[index].statusClass = 'accepted'

          uni.showToast({
            title: '验收成功',
            icon: 'success'
          })
        }
      }
    }
  })
}

// 去支付
const payStage = (index) => {
  uni.showToast({
    title: '跳转支付页面',
    icon: 'loading'
  })
  // 这里可以跳转到支付页面
}

// 终止辅导
const terminateOrder = () => {
  showTerminateModal.value = true
}

// 关闭终止辅导弹窗
const closeTerminateModal = () => {
  showTerminateModal.value = false
}

// 确认终止辅导
const confirmTerminate = async () => {
  const result = await handleApiResponse(
      orderApi.terminateOrder({ orderId: orderId.value }),
      {
        showLoading: true
      }
  )
  if (result.success) {
    uni.showToast({
      title: '辅导已终止',
      icon: 'success'
    })

    showTerminateModal.value = false
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
}

// 加载订单详情
const loadOrderDetail = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const response = await orderApi.getStudentOrderDetail(orderId.value)

    if (response.code === '1000') {
      orderDetail.value = response.result || {}
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

onLoad((options) => {
  if (options && options.orderId) {
    orderId.value = options.orderId
    loadOrderDetail()
    // 加载交付信息
    loadDeliveryInfoList()
  }
})

const loadDeliveryInfoList = async () => {
  if (!orderId.value) return

  const result = await handleApiResponse(
      deliveryInfoApi.getStudentDeliveryInfo({ orderId: orderId.value }),
      {
        showLoading: true
      }
  )

  if (result.success) {
    deliveryStageInfo.value = result.data
  }

}

const stageStatusClassArray = ['pending', 'wait-deliver', 'wait-confirm', 'accepted']

const getStageStatusClass = (stageStatus) => {
  return stageStatusClassArray[stageStatus]
}

</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0 16rpx 0;
  color: #888;
  border-bottom: 4rpx solid transparent;
  font-size: 28rpx;
}

.tab-item.active {
  color: #1e98d7;
  border-bottom: 4rpx solid #1e98d7;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 0 32rpx 120rpx 32rpx;
  background: #fff;
}

.info-container {
  padding: 32rpx 0;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #666;
  text-align: right;
}

/* 章节标题 */
.info-section {
  padding: 32rpx 0 0 0;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
}

/* 描述文本 */
.description-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 老师要求 */
.teacher-requirements {
  padding-bottom: 32rpx;
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.requirement-label {
  font-size: 28rpx;
  color: #333;
}

.requirement-tag {
  background: #1e98d7;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.requirement-tag.no-requirement {
  background: #f5f5f5;
  color: #999;
}

.teacher-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-top: 16rpx;
}

/* 交付信息 */
.delivery-info {
  padding: 32rpx 0;
}

/* 辅导老师部分 */
.teacher-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 32rpx;
}

.teacher-label {
  font-size: 28rpx;
  color: #333;
}

.teacher-link {
  font-size: 28rpx;
  color: #1e98d7;
}

/* 交付阶段表格 */
.delivery-table {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 1rpx solid #e0e0e0;
}

.header-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.stage {
  flex: 1.2;
}

.header-cell.amount {
  flex: 1;
}

.header-cell.status {
  flex: 1.2;
}

.header-cell.action {
  flex: 1.5;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.stage {
  flex: 1.2;
}

.table-cell.amount {
  flex: 1;
}

.table-cell.status {
  flex: 1.2;
}

.table-cell.action {
  flex: 1.5;
}

.table-cell.status.accepted {
  color: #4caf50;
}

.table-cell.status.pending {
  color: #ff9800;
}

.table-cell.status.wait-deliver {
  color: #d84f26;
}

.table-cell.status.wait-confirm {
  color: #2196f3;
}

.confirm-btn {
  background: #1e98d7;
  color: #fff;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.pay-btn {
  background: #1e98d7;
  color: #fff;
  border: none;
  padding: 12rpx 24rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.total-row {
  display: flex;
  background: #f9f9f9;
  border-top: 2rpx solid #e0e0e0;
}

.total-label {
  flex: 1.2;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
}

.total-amount {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #f44336;
  font-weight: bold;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
}

.total-status {
  flex: 1.2;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #ff9800;
  font-weight: bold;
  text-align: center;
  border-right: 1rpx solid #e0e0e0;
}

.total-action {
  flex: 1.5;
  padding: 24rpx 16rpx;
  border-right: none;
}

/* 注释说明 */
.notes-section {
  margin-bottom: 32rpx;
}

.notes-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.notes-item {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

/* 详细信息表格 */
.detail-table {
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 120rpx;
  overflow: hidden;
}

.detail-row {
  display: flex;
  border-bottom: 1rpx solid #e0e0e0;
  min-height: 80rpx;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  width: 160rpx;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  background: #f5f5f5;
  border-right: 1rpx solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.detail-value {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #666;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.status-text {
  display: block;
  line-height: 1.6;
  margin-bottom: 8rpx;
}

.status-text:last-child {
  margin-bottom: 0;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.05);
}

.terminate-btn {
  width: 100%;
  padding: 24rpx 0;
  background: #ffd54f;
  color: #333;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

/* 终止辅导弹窗 */
.terminate-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 48rpx 32rpx 32rpx 32rpx;
  margin: 0 64rpx;
  min-width: 500rpx;
}

.modal-title {
  font-size: 32rpx;
  color: #333;
  text-align: center;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.modal-actions {
  display: flex;
  gap: 24rpx;
}

.modal-btn {
  flex: 1;
  padding: 20rpx 0;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  text-align: center;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #1e98d7;
  color: #fff;
}
</style>
