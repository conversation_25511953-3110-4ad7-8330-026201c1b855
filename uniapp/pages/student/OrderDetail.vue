<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{ active: activeTab === 'info' }" @click="switchTab('info')">基本信息</view>
      <view class="tab-item" :class="{ active: activeTab === 'applicants' }" @click="switchTab('applicants')">投递人员</view>
    </view>

    <!-- 基本信息内容 -->
    <view class="content" v-if="activeTab === 'info'">
      <view class="info-item">
        <text class="info-label">订单号</text>
        <text class="info-value">{{ order.no || 'JS13455555333' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">发布时间</text>
        <text class="info-value">{{ order.date || '2025-3-12 12:11:12' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">辅导项目</text>
        <text class="info-value">{{ order.type || '作业' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习阶段</text>
        <text class="info-value">{{ order.stage || '本科' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习位置</text>
        <text class="info-value">{{ order.location || '国内' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习学校</text>
        <text class="info-value">{{ order.school || '北京大学' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习专业</text>
        <text class="info-value">{{ order.major || '会计' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">预算区间</text>
        <text class="info-value budget">{{ order.budget || '1000-2000元' }}</text>
      </view>

      <!-- 老师要求 -->
      <view class="section-title">老师要求</view>

      <view class="teacher-requirements">
        <view class="requirement-item">
          <text class="requirement-label">老师情况</text>
          <view class="requirement-tags">
            <text class="tag active">{{ order.teacherDegree || '博士' }}</text>
          </view>
        </view>

        <view class="requirement-item">
          <text class="requirement-label">是否留学</text>
          <view class="requirement-tags">
            <text class="tag">{{ order.abroad || '不要求' }}</text>
          </view>
        </view>
      </view>

      <!-- 需求描述 -->
      <view class="section-title">需求描述</view>
      <view class="description">
        {{ order.description || '想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师' }}
      </view>

      <!-- 其他说明 -->
      <view class="section-title">其他说明</view>
      <view class="description">
        {{ order.extra || '希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心。' }}
      </view>
    </view>

    <!-- 投递人员内容 -->
    <view class="content applicants-content" v-if="activeTab === 'applicants'">
      <!-- 不合适人数统计 -->
      <view class="unsuitable-count">
        <text class="count-badge">不合适：{{ unsuitableCount }}</text>
      </view>

      <!-- 报名人列表 -->
      <view class="applicant-list">
        <view class="applicant-card" v-for="(applicant, index) in applicants" :key="index">
          <view class="card-content" @click="goToTeacherDetail(applicant)">
            <!-- 头像和基本信息 -->
            <view class="applicant-header">
              <image class="avatar" :src="applicant.avatar" mode="aspectFill"></image>
              <view class="applicant-info">
                <view class="name-status-row">
                  <text class="name">{{ applicant.name }}</text>
                  <text class="verified-badge">已实名认证</text>
                </view>
                <text class="education">{{ applicant.education }}</text>
                <text class="tutored-count">已辅导：{{ applicant.tutoredCount || 12 }}</text>
                <text class="experience">{{ applicant.experience }}</text>
              </view>
            </view>

            <!-- 标签 -->
            <view class="tags-row">
              <text class="tag" v-for="(tag, tagIndex) in applicant.tags" :key="tagIndex">{{ tag }}</text>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <button class="btn btn-reject" @click="rejectApplicant(index)">不合适</button>
              <button class="btn btn-contact" @click="contactApplicant(index)">复制微信</button>
              <button class="btn btn-hire" @click="hireApplicant(index)">确认老师</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="bottom-action">
      <button class="evaluate-btn" @click="goEvaluate">去评价</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useOrderStore } from '../../../store/order.js'

const orderStore = useOrderStore()
const orderId = ref('')
const activeTab = ref('info') // 当前激活的标签页

// 使用computed获取当前订单信息
const order = computed(() => {
  if (orderId.value) {
    return orderStore.getOrderById(orderId.value) || {}
  }
  return orderStore.currentOrder || {}
})

// 报名人员数据
const applicants = ref([
  {
    name: '张伟｜博士',
    education: '清华大学｜博士｜环境会计',
    experience: '我是来自清华大学的张飞，在读博士，已发表2篇SCI，已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇......',
    avatar: '/static/images/avatar1.png',
    verified: true,
    tutoredCount: 12,
    tags: ['论文辅导', '专业课辅导', '保研辅导']
  },
  {
    name: '张伟｜博士',
    education: '清华大学｜博士｜环境会计',
    experience: '我是来自清华大学的张飞，在读博士，已发表2篇SCI，已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇SCI已发表2篇......',
    avatar: '/static/images/avatar2.png',
    verified: true,
    tutoredCount: 12,
    tags: ['论文辅导', '专业课辅导', '保研辅导']
  }
])

// 不合适人数
const unsuitableCount = ref(10)

// 切换标签页
const switchTab = (tab) => {
  activeTab.value = tab
}

const goBack = () => {
  uni.navigateBack()
}

const goEvaluate = () => {
  uni.navigateTo({
    url: '/uniapp/pages/student/Evaluate?orderId=' + order.value.no
  })
}

// 报名人员操作方法
const rejectApplicant = (index) => {
  uni.showToast({
    title: '已标记为不合适',
    icon: 'success'
  })
}

const contactApplicant = (index) => {
  uni.showToast({
    title: '微信号已复制',
    icon: 'success'
  })
}

const hireApplicant = (index) => {
  uni.showModal({
    title: '确认聘请',
    content: '确定要聘请这位老师吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '聘请成功',
          icon: 'success'
        })
      }
    }
  })
}

// 跳转到老师详情页面
const goToTeacherDetail = (teacher) => {
  uni.navigateTo({
    url: '/uniapp/pages/student/TeacherDetail?teacher=' + encodeURIComponent(JSON.stringify(teacher))
  })
}

onLoad((options) => {
  if (options && options.orderId) {
    orderId.value = options.orderId
  } else if (options && options.order) {
    // 兼容旧的传参方式
    const data = JSON.parse(decodeURIComponent(options.order))
    orderStore.setCurrentOrder(data)
  }
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  font-size: 30rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}

.tab-item.active {
  color: #333;
  font-weight: bold;
  border-bottom-color: #333;
}

/* 内容区域 */
.content {
  flex: 1;
  background: #fff;
  padding: 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #333;
}

.info-value {
  font-size: 30rpx;
  color: #666;
}

.budget {
  color: #ff6b35;
  font-weight: bold;
}

/* 区域标题 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin: 40rpx 0 24rpx 0;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 老师要求 */
.teacher-requirements {
  margin-bottom: 40rpx;
}

.requirement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
}

.requirement-label {
  font-size: 30rpx;
  color: #333;
}

.requirement-tags {
  display: flex;
  gap: 16rpx;
}

.tag {
  padding: 8rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  background: #f0f0f0;
  color: #666;
}

.tag.active {
  background: #007aff;
  color: #fff;
}

/* 描述文本 */
.description {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

/* 底部按钮 */
.bottom-action {
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.evaluate-btn {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 投递人员样式 */
.applicants-content {
  padding: 0;
  background: #f5f5f5;
}

.unsuitable-count {
  padding: 24rpx 32rpx;
  background: #fff;
  margin-bottom: 16rpx;
}

.count-badge {
  display: inline-block;
  background: #ff6b6b;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.applicant-list {
  padding: 0 32rpx;
}

.applicant-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.card-content {
  padding: 32rpx;
}

.applicant-header {
  display: flex;
  margin-bottom: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.applicant-info {
  flex: 1;
}

.name-status-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.verified-badge {
  background: #ffa726;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.education {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.tutored-count {
  font-size: 28rpx;
  color: #007aff;
  margin-bottom: 12rpx;
}

.experience {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.tags-row .tag {
  background: #f0f0f0;
  color: #666;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.btn {
  flex: 1;
  height: 72rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.btn-reject {
  background: #999;
  color: #fff;
}

.btn-contact {
  background: #17a2b8;
  color: #fff;
}

.btn-hire {
  background: #007aff;
  color: #fff;
}
</style>
