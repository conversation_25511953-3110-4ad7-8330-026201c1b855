<template>
  <view class="confirm-teacher">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">确认老师</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips-section">
      <text class="tips-title">提示：</text>
      <text class="tips-text">1、请和辅导老师协商清楚，再进行确认。</text>
      <text class="tips-text">2、请和老师确认付款和对价格，确认后提交写交付阶段高，提交后</text>
      <text class="tips-text">不允许修改。</text>
      <text class="tips-text">3、每个阶段允许，再交付。</text>
      <text class="tips-text">4、您付的款都在平台托管，在订单结束14日内如果无异议，辅导</text>
      <text class="tips-text">老师才能提现。</text>
    </view>

    <!-- 交付阶段 -->
    <view class="delivery-section">
      <view class="section-header">
        <text class="section-title">交付阶段</text>
        <text class="section-subtitle">阶段费用</text>
      </view>

      <!-- 阶段列表 -->
      <view class="stage-list">
        <view class="stage-item" v-for="(stage, index) in stages" :key="index">
          <text class="stage-name">第{{ stage.deliveryStage }}阶段</text>
          <view class="stage-input">
            <input 
              class="amount-input" 
              type="number" 
              placeholder="请输入金额"
              v-model="stage.deliveryAmount"
            />
          </view>
          <view class="stage-actions">
            <view class="action-btn minus-btn" @click="removeStage(index)" v-if="stages.length > 1">
              <text class="action-text">-</text>
            </view>
            <view class="action-btn plus-btn" @click="addStage">
              <text class="action-text">+</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 总价 -->
      <view class="total-section">
        <text class="total-label">共{{ stages.length }}阶段</text>
        <text class="total-amount">{{ totalAmount }}</text>
      </view>
    </view>

    <!-- 底部确认按钮 -->
    <view class="bottom-actions">
      <button class="confirm-btn" @click="confirmOrder">确认订单</button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import {handleApiResponse} from "../../../utils/apiHelper";
import {deliveryInfoApi} from "../../../api";

const teacherId = ref()
const orderId = ref()
const stages = ref([
  { deliveryStage: 1, deliveryAmount: 0 },
])

// 计算总金额
const totalAmount = computed(() => {
  const total = stages.value.reduce((sum, stage) => {
    return sum + (parseFloat(stage.deliveryAmount) || 0)
  }, 0)
  return total.toFixed(3)
})

const goBack = () => {
  uni.navigateBack()
}

const addStage = () => {
  // 最多可加10个
  if (stages.value.length === 10) {
    uni.showToast({
      title: '最多可添加10个交付阶段',
      icon: 'none'
    })
    return
  }
  const stageNumber = stages.value.length + 1
  stages.value.push({
    deliveryStage: stageNumber,
    deliveryAmount: ''
  })
}

const removeStage = (index) => {
  if (stages.value.length > 1) {
    stages.value.splice(index, 1)
    // 重新编号
    stages.value.forEach((stage, idx) => {
      stage.deliveryStage = idx + 1
    })
  }
}

const confirmOrder = () => {
  // 验证所有阶段都有金额
  const hasEmptyAmount = stages.value.some(stage => !stage.deliveryAmount || parseFloat(stage.deliveryAmount) <= 0)
  
  if (hasEmptyAmount) {
    uni.showToast({
      title: '请填写所有阶段的金额',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '确认订单',
    content: `确定要确认此订单吗？总金额：${totalAmount.value}元`,
    success: async (res) => {
      if (res.confirm) {
        const params = {
          teacherId: teacherId.value,
          orderId: orderId.value,
          deliveryInfoList: stages.value
        }
        const result = await handleApiResponse(
            deliveryInfoApi.confirm(params),
            {
              showLoading: true,
            }
        )

        if (result.success) {
          uni.showToast({
            title: '订单确认成功',
            icon: 'success'
          })

          setTimeout(() => {
            uni.navigateTo({url: '/uniapp/pages/student/Orders'})
          }, 1500)
        }

      }
    }
  })
}

onLoad((options) => {
  if (options) {
    teacherId.value = options.teacherId
    orderId.value = options.orderId

    console.log('订单和老师信息：', orderId.value, teacherId.value)
  }
})
</script>

<style scoped>
.confirm-teacher {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 提示信息 */
.tips-section {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 16rpx;
}

.tips-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 8rpx;
}

/* 交付阶段 */
.delivery-section {
  background: #fff;
  padding: 32rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-subtitle {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 阶段列表 */
.stage-list {
  margin-bottom: 32rpx;
}

.stage-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stage-item:last-child {
  border-bottom: none;
}

.stage-name {
  font-size: 28rpx;
  color: #333;
  width: 160rpx;
}

.stage-input {
  flex: 1;
  margin: 0 32rpx;
}

.amount-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  text-align: center;
}

.stage-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.minus-btn {
  background: #42a5f5;
}

.plus-btn {
  background: #42a5f5;
}

.action-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 总价 */
.total-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.total-label {
  font-size: 28rpx;
  color: #333;
}

.total-amount {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4444;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.confirm-btn {
  width: 100%;
  height: 88rpx;
  background: #42a5f5;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
