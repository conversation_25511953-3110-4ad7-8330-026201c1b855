<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{ active: activeTab === 'info' }" @click="switchTab('info')">基本信息</view>
      <view class="tab-item" :class="{ active: activeTab === 'applicants' }" @click="switchTab('applicants')">投递人员 ({{ order.deliveryCount }})</view>
    </view>

    <!-- 基本信息内容 -->
    <view class="content" v-if="activeTab === 'info'">
      <view class="info-item">
        <text class="info-label">订单号</text>
        <text class="info-value">{{ order.orderNo }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">发布时间</text>
        <text class="info-value">{{ order.createTime }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">辅导项目</text>
        <text class="info-value">{{ getTutoringItemText(order.tutoringItem) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习阶段</text>
        <text class="info-value">{{ getStudyStageText(order.studyStage) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习位置</text>
        <text class="info-value">{{ getStudyPlaceText(order.studyPlace) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学习专业</text>
        <text class="info-value">{{ order.studyMajor || '未填写' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">预算区间</text>
        <text class="info-value">{{ formatBudget(order.budgetMin, order.budgetMax) }}</text>
      </view>

      <view class="info-section">
        <text class="section-title">老师要求</text>
      </view>

      <view class="teacher-requirements">
        <view class="requirement-row">
          <text class="requirement-label">老师情况</text>
        </view>
        <view class="requirement-tags">
          <text class="requirement-tag active">{{ getTeacherLevelText(order.teacherLevel) }}</text>
        </view>

        <view class="requirement-row">
          <text class="requirement-label">是否留学</text>
        </view>
        <view class="requirement-tags">
          <text class="requirement-tag active">{{ getTeacherAbroadText(order.teacherAbroad) }}</text>
        </view>
      </view>

      <view class="info-section">
        <text class="section-title">需求描述</text>
      </view>

      <view class="description-text">
        {{ order.requirementText || '暂无描述' }}
      </view>

      <view class="info-section" v-if="order.comment">
        <text class="section-title">其他说明</text>
      </view>

      <view class="description-text" v-if="order.comment">
        {{ order.comment }}
      </view>

      <view class="info-section">
        <text class="section-title">投递统计</text>
      </view>

      <view class="delivery-stats">
        <text class="stats-text">已有 {{ order.deliveryCount }} 人投递</text>
      </view>
    </view>

    <!-- 投递人员内容 -->
    <view class="content" v-if="activeTab === 'applicants'">
      <!-- 统计信息 -->
<!--      <view class="stats-header">-->
<!--        <text class="reject-count">不合适: {{ rejectedCount }}</text>-->
<!--      </view>-->

      <!-- 报名人列表 -->
      <scroll-view
          class="applicant-list"
          scroll-y="true"
          :style="{ height: scrollHeight + 'px' }"
          @scrolltolower="loadMore"
          :lower-threshold="100"
      >
        <view class="applicant-card" v-for="(applicant, index) in applicants" :key="index">
          <view class="card-content" @click="goToTeacherDetail(applicant)">
            <!-- 头像和基本信息 -->
            <view class="applicant-header">
              <image class="avatar" :src="applicant.avatar" mode="aspectFill"></image>
              <view class="applicant-info">
                <view class="name-status-row">
                  <text class="name">{{ applicant.nickname }}</text>
                  <text class="verified-badge">已实名认证</text>
                </view>
                <text class="education">{{ constants.TEACHER_LEVEL_TEXT[applicant.educationLevel] || '' }}</text>
                <text class="tutored-count">已辅导：{{ applicant.tutoringCount || 0 }}</text>
                <text class="experience">{{ applicant.experience }}</text>
              </view>
            </view>

            <!-- 标签 -->
            <view class="tags-row">
              <text v-for="item in applicant.goodAtItems" :key="item" class="tag">
                {{ constants.GOOD_AT_ITEMS_TEXT[item] || '' }}辅导
              </text>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="action-buttons">
<!--            <button class="action-btn reject-btn" @click.stop="rejectApplicant(index)">不合适</button>-->
            <button class="action-btn contact-btn" @click.stop="contactApplicant(applicant.wechatNo)">复制微信</button>
            <button class="action-btn hire-btn" @click.stop="hireApplicant(applicant)">确认老师</button>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部操作按钮 - 仅在基本信息页面显示 -->
    <view class="bottom-actions" v-if="activeTab === 'info'">
      <button class="action-button delete-btn" @click="deleteOrder">删除订单</button>
      <button class="action-button modify-btn" v-if="order.modifyBtnShow" @click="modifyOrder">修改信息</button>
    </view>
  </view>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useOrderStore } from '../../../store/order.js'
import {orderApi, constants, teacherApi} from '../../../api'
import {handleApiResponse, handlePaginationData} from "../../../utils/apiHelper";

const orderStore = useOrderStore()
const orderId = ref('')
const activeTab = ref('info') // 当前激活的标签页
const loading = ref(false)
const orderDetail = ref({})
const scrollHeight = ref(600)

// 使用computed获取当前订单信息
const order = computed(() => {
  return orderDetail.value
})

// 拒绝统计
const rejectedCount = ref(10)

// 报名人员数据
const applicants = ref([])
const loadingMore = ref(false)
const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
  hasMore: true
})

const goBack = () => {
  uni.navigateBack()
}

const switchTab = (tab) => {
  activeTab.value = tab
}

// 加载订单详情
const loadOrderDetail = async () => {
  if (!orderId.value) return

  loading.value = true
  try {
    const response = await orderApi.getStudentOrderDetail(orderId.value)

    if (response.code === '1000') {
      orderDetail.value = response.result || {}
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'error'
      })
    }
  } catch (error) {
    console.error('加载订单详情失败:', error)
    uni.showToast({
      title: '网络错误',
      icon: 'error'
    })
  } finally {
    loading.value = false
  }
}

// 获取订单投递人员列表
const getDeliverTeacherList = async () => {
  if (!orderId.value) return

  loading.value = true
  loadingMore.value = true

  const params = {
    orderId: orderId.value,
    current: pagination.value.current,
    size: pagination.value.size,
  }

  const result = await handleApiResponse(
      teacherApi.getDeliverTeacherList(params),
      {
        showLoading: false,
        showError: true
      }
  )

  loading.value = false
  loadingMore.value = false

  if (result.success) {

    const paginationData = handlePaginationData(
        { result: result.data },
        applicants.value,
        false
    )

    applicants.value = paginationData.list
    pagination.value = paginationData.pagination
  }

}

// 加载更多数据
const loadMore = async () => {
  if (!pagination.value.hasMore || loadingMore.value) return

  pagination.value.current += 1
  await getDeliverTeacherList()
}

// 获取辅导项目文本
const getTutoringItemText = (item) => {
  return constants.TUTORING_ITEM_TEXT[item] || '未知'
}

// 获取学习阶段文本
const getStudyStageText = (stage) => {
  return constants.STUDY_STAGE_TEXT[stage] || '未知'
}

// 获取学习场所文本
const getStudyPlaceText = (place) => {
  return constants.STUDY_PLACE_TEXT[place] || '未知'
}

// 获取老师学历文本
const getTeacherLevelText = (level) => {
  return constants.TEACHER_LEVEL_TEXT[level] || '未知'
}

// 获取老师留学要求文本
const getTeacherAbroadText = (abroad) => {
  return constants.TEACHER_ABROAD_TEXT[abroad] || '未知'
}

// 格式化预算区间
const formatBudget = (min, max) => {
  return `${min}-${max}元`
}

// 删除订单
const deleteOrder = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个订单吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        const result = await handleApiResponse(
            orderApi.delOrder({ id: orderId.value }),
            {
              showLoading: true,
            }
        )

        if (result.success) {
          uni.showToast({
            title: '订单已删除',
            icon: 'success'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }

      }
    }
  })
}

// 修改订单信息
const modifyOrder = () => {
  uni.navigateTo({
    url: '/uniapp/pages/student/Publish?orderId=' + order.value.id
  })
}

// 报名人员操作方法
const rejectApplicant = (index) => {
  uni.showToast({
    title: '已标记为不合适',
    icon: 'success'
  })
}

const contactApplicant = (wechatNo) => {
  uni.setClipboardData({
    data: wechatNo,
    success: () => {
      uni.showToast({
        title: '已复制成功，可以添加微信，备注：来自辅导君',
        icon: 'success'
      })
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'error'
      })
    }
  })
}

const hireApplicant = (teacher) => {
  // 跳转页面
  uni.navigateTo({url: '/uniapp/pages/student/ConfirmTeacher?teacherId=' + teacher.id + '&orderId=' + orderId.value})

}

// 跳转到老师详情页面
const goToTeacherDetail = (teacher) => {
  uni.navigateTo({
    url: '/uniapp/pages/student/TeacherDetail?teacherId=' + teacher.id + '&orderId=' + orderId.value,
  })
}

onMounted(() => {
  // 计算滚动区域高度
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 240 // 减去导航栏、标签页和tabbar高度
})

onLoad((options) => {
  if (options && options.orderId) {
    orderId.value = options.orderId
    loadOrderDetail()
    getDeliverTeacherList()
  }
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0 16rpx 0;
  color: #888;
  border-bottom: 4rpx solid transparent;
  font-size: 28rpx;
}

.tab-item.active {
  color: #1e98d7;
  border-bottom: 4rpx solid #1e98d7;
  font-weight: bold;
}

/* 内容区域 */
.content {
  flex: 1;
  padding: 0 32rpx 120rpx 32rpx;
}

/* 信息项 */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.info-value {
  font-size: 28rpx;
  color: #666;
  text-align: right;
}

/* 老师要求 */
.teacher-requirements {
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.requirement-row {
  margin-bottom: 16rpx;
}

.requirement-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.requirement-tags {
  display: flex;
  gap: 16rpx;
}

.requirement-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: #f0f0f0;
  color: #666;
}

.requirement-tag.active {
  background: #1e98d7;
  color: #fff;
}

/* 章节标题 */
.info-section {
  padding: 32rpx 0 16rpx 0;
}

.section-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 描述文本 */
.description-text {
  padding: 16rpx 0 32rpx 0;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 统计信息 */
.stats-header {
  display: flex;
  justify-content: flex-end;
  padding: 16rpx 0;
}

.reject-count {
  background: #ff9999;
  color: #fff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 报名人列表 */
.applicant-list {
  padding-top: 24rpx;
}

.applicant-card {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.card-content {
  padding: 32rpx;
}

.applicant-header {
  display: flex;
  margin-bottom: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.applicant-info {
  flex: 1;
}

.name-status-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 16rpx;
}

.verified-badge {
  background: #ffa726;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
}

.education {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.tutored-count {
  font-size: 28rpx;
  color: #007aff;
  margin-bottom: 12rpx;
}

.experience {
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.tags-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.tags-row .tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
  padding: 0 32rpx 32rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 24rpx;
}

.action-btn {
  flex: 1;
  padding: 16rpx 0;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  text-align: center;
}

.reject-btn {
  background: #f5f5f5;
  color: #666;
}

.contact-btn {
  background: #4fc3f7;
  color: #fff;
}

.hire-btn {
  background: #4fc3f7;
  color: #fff;
}

/* 底部操作按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.05);
}

.action-button {
  flex: 1;
  padding: 28rpx 0;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  text-align: center;
}

/* 投递统计样式 */
.delivery-stats {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin: 16rpx 0;
}

.stats-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.stats-text:last-child {
  margin-bottom: 0;
  color: #007aff;
}

.delete-btn {
  background: #f8f8f8;
  color: #666;
  border: 1rpx solid #e0e0e0;
}

.modify-btn {
  background: #1e98d7;
  color: #fff;
}
</style>
