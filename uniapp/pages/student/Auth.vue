<template>
  <view class="auth">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">辅导君</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 基本信息表单 -->
    <view class="form-section">
      <view class="form-item">
        <text class="form-label">真实姓名</text>
        <input
          class="form-input"
          v-model="formData.realName"
          placeholder="请输入您的真实姓名"
        />
      </view>

      <view class="form-item">
        <text class="form-label">手机号</text>
        <input
          class="form-input"
          v-model="formData.phone"
          placeholder="请输入手机号"
        />
      </view>

      <view class="form-item">
        <text class="form-label">身份证号</text>
        <input
          class="form-input"
          v-model="formData.idCard"
          placeholder="110101199001010000"
        />
      </view>

      <view class="form-item">
        <text class="form-label">毕业学校</text>
        <input
          class="form-input"
          v-model="formData.school"
          placeholder="请输入毕业学校名称"
        />
      </view>

      <view class="form-item">
        <text class="form-label">专业学历</text>
        <view class="degree-selector">
          <text
            class="degree-option"
            :class="{ active: formData.degree === '本科' }"
            @click="selectDegree('本科')"
          >本科</text>
          <text
            class="degree-option"
            :class="{ active: formData.degree === '硕士' }"
            @click="selectDegree('硕士')"
          >硕士</text>
          <text
            class="degree-option"
            :class="{ active: formData.degree === '博士' }"
            @click="selectDegree('博士')"
          >博士</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">专业或研究方向</text>
        <input
          class="form-input"
          v-model="formData.major"
          placeholder="请输入专业或研究方向"
        />
      </view>
    </view>

    <!-- 科研经历 -->
    <view class="form-section">
      <view class="section-title">科研经历（请填写您的真实经历，一定要真实有效，否则会影响问题解答）</view>
      <textarea
        class="form-textarea"
        v-model="formData.researchExperience"
        placeholder="院系老师的研究经历，以及你的研究经历，让学生了解你的能力，提高接单率。例如：2022-2024年，我在清华大学环境学院从事环境会计相关研究，已发表2篇SCI论文，1000字"
      ></textarea>
    </view>

    <!-- 个人自述 -->
    <view class="form-section">
      <view class="section-title">个人自述</view>
      <textarea
        class="form-textarea"
        v-model="formData.personalStatement"
        placeholder="写一段话让学生了解你，以及你的优势，提高接单率。（个人经历，以及你的优势，让学生了解你的能力，提高接单率，1000字）"
      ></textarea>
    </view>

    <!-- 个人证件 -->
    <view class="form-section">
      <view class="section-title">个人证件（个人证件的作用是帮助你提高接单率）</view>

      <view class="upload-section">
        <view class="upload-item">
          <text class="upload-label">身份证人像面</text>
          <view class="upload-btn" @click="uploadIdCardFront">
            <text class="upload-icon">+</text>
            <text class="upload-text">身份证人像面</text>
          </view>
        </view>

        <view class="upload-item">
          <text class="upload-label">身份证国徽面</text>
          <view class="upload-btn" @click="uploadIdCardBack">
            <text class="upload-icon">+</text>
            <text class="upload-text">身份证国徽面</text>
          </view>
        </view>

        <view class="upload-item">
          <text class="upload-label">学生证、毕业证、学位证</text>
          <view class="upload-btn" @click="uploadEducationCert">
            <text class="upload-icon">+</text>
            <text class="upload-text">学生证、毕业证、学位证</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitAuth">提交审核</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '../../../store/user.js'

const userStore = useUserStore()

// 表单数据
const formData = ref({
  realName: '',
  phone: '',
  idCard: '',
  school: '',
  degree: '',
  major: '',
  researchExperience: '',
  personalStatement: '',
  idCardFront: '',
  idCardBack: '',
  educationCert: ''
})

const selectDegree = (degree) => {
  formData.value.degree = degree
}

const uploadIdCardFront = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      formData.value.idCardFront = res.tempFilePaths[0]
      uni.showToast({
        title: '身份证人像面上传成功',
        icon: 'success'
      })
    }
  })
}

const uploadIdCardBack = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      formData.value.idCardBack = res.tempFilePaths[0]
      uni.showToast({
        title: '身份证国徽面上传成功',
        icon: 'success'
      })
    }
  })
}

const uploadEducationCert = () => {
  uni.chooseImage({
    count: 3,
    success: (res) => {
      formData.value.educationCert = res.tempFilePaths.join(',')
      uni.showToast({
        title: '学历证件上传成功',
        icon: 'success'
      })
    }
  })
}

const submitAuth = () => {
  // 验证必填字段
  if (!formData.value.realName || !formData.value.phone || !formData.value.idCard ||
      !formData.value.school || !formData.value.degree || !formData.value.major) {
    uni.showToast({
      title: '请填写完整的基本信息',
      icon: 'error'
    })
    return
  }

  // 提交认证信息
  uni.showLoading({
    title: '提交中...'
  })

  // 模拟提交过程
  setTimeout(() => {
    uni.hideLoading()

    // 更新用户信息和角色
    userStore.setUserInfo({
      ...formData.value,
      role: 'teacher' // 设置用户角色为教师
    })

    // 设置认证状态为已通过（模拟直接通过）
    userStore.setAuthInfo({
      status: 'verified',
      submitTime: new Date().toLocaleString()
    })

    uni.showToast({
      title: '认证成功！',
      icon: 'success',
      duration: 2000
    })

    // 跳转到教师首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/uniapp/pages/teacher/Home'
      })
    }, 2000)
  }, 2000)
}

const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.auth {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 36rpx;
  color: #333;
  padding: 0 8rpx;
}

/* 表单区域 */
.form-section {
  background: #fff;
  margin: 0 24rpx 24rpx 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.form-item {
  margin-bottom: 32rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
}

.form-input:focus {
  border-color: #007aff;
}

.form-textarea {
  width: 100%;
  min-height: 200rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #333;
  background: #fff;
  line-height: 1.5;
}

.form-textarea:focus {
  border-color: #007aff;
}

.degree-selector {
  display: flex;
  gap: 24rpx;
}

.degree-option {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666;
  background: #fff;
}

.degree-option.active {
  border-color: #007aff;
  color: #007aff;
  background: #f0f8ff;
}

/* 区域标题 */
.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

/* 上传区域 */
.upload-section {
  margin-top: 24rpx;
}

.upload-item {
  margin-bottom: 32rpx;
}

.upload-item:last-child {
  margin-bottom: 0;
}

.upload-label {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
}

.upload-btn {
  width: 100%;
  height: 120rpx;
  border: 2rpx dashed #d0d0d0;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 24rpx;
  color: #999;
}

/* 提交区域 */
.submit-section {
  padding: 0 24rpx;
  margin-top: 48rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
