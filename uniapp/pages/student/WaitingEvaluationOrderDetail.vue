<template>
  <view class="evaluation-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">评价</text>
    </view>

    <!-- 订单完成状态 -->
    <view class="completion-status">
      <text class="completion-title">订单已完成</text>
    </view>

    <!-- 评价内容 -->
    <view class="evaluation-content">
      <!-- 综合评价 -->
      <view class="rating-section">
        <text class="rating-label">综合评价</text>
        <view class="stars">
          <view class="star-container" v-for="n in 5" :key="n" @click="setRating('overall', n)">
            <text class="star full"
                  :class="{ active: n <= overallRating }">★</text>
            <text class="star half"
                  :class="{ active: overallRating >= n - 0.5 && overallRating < n }"
                  @click.stop="setRating('overall', n - 0.5)">★</text>
          </view>
        </view>
        <text class="rating-score">{{ overallRating }}分</text>
      </view>

      <!-- 专业度评价 -->
      <view class="rating-section">
        <text class="rating-label">专业度</text>
        <view class="stars">
          <view class="star-container" v-for="n in 5" :key="n" @click="setRating('professional', n)">
            <text class="star full"
                  :class="{ active: n <= professionalRating }">★</text>
            <text class="star half"
                  :class="{ active: professionalRating >= n - 0.5 && professionalRating < n }"
                  @click.stop="setRating('professional', n - 0.5)">★</text>
          </view>
        </view>
        <text class="rating-score">{{ professionalRating }}分</text>
      </view>

      <!-- 服务态度评价 -->
      <view class="rating-section">
        <text class="rating-label">服务态度</text>
        <view class="stars">
          <view class="star-container" v-for="n in 5" :key="n" @click="setRating('service', n)">
            <text class="star full"
                  :class="{ active: n <= serviceRating }">★</text>
            <text class="star half"
                  :class="{ active: serviceRating >= n - 0.5 && serviceRating < n }"
                  @click.stop="setRating('service', n - 0.5)">★</text>
          </view>
        </view>
        <text class="rating-score">{{ serviceRating }}分</text>
      </view>

      <!-- 评价内容 -->
      <view class="comment-section">
        <text class="comment-label">评价内容</text>
        <textarea
          class="comment-input"
          v-model="comment"
          placeholder="请描述您对本次辅导的评价（选填）"
          maxlength="300">
        </textarea>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button class="submit-btn" @click="submitEvaluation">提交评价</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useOrderStore } from '../../../store/order.js'

const orderStore = useOrderStore()
const orderId = ref('')

// 评分数据
const overallRating = ref(5)
const professionalRating = ref(5)
const serviceRating = ref(5)
const comment = ref('')

const goBack = () => {
  uni.navigateBack()
}

// 设置评分
const setRating = (type, rating) => {
  switch(type) {
    case 'overall':
      overallRating.value = rating
      break
    case 'professional':
      professionalRating.value = rating
      break
    case 'service':
      serviceRating.value = rating
      break
  }
}

// 提交评价
const submitEvaluation = () => {
  const evaluationData = {
    overallRating: overallRating.value,
    professionalRating: professionalRating.value,
    serviceRating: serviceRating.value,
    comment: comment.value,
    date: new Date().toLocaleString()
  }

  // 调用store方法完成评价，更新订单状态为已评价(3)
  orderStore.completeEvaluation(orderId.value, evaluationData)

  uni.showToast({
    title: '评价提交成功，感谢您的评价，老师也会继续加油的',
    icon: 'success'
  })

  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}

onLoad((options) => {
  if (options && options.orderId) {
    orderId.value = options.orderId
  }
})
</script>

<style scoped>
.evaluation-page {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

/* 完成状态 */
.completion-status {
  background: #fff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  text-align: center;
}

.completion-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4caf50;
}

/* 评价内容 */
.evaluation-content {
  background: #fff;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

/* 评分区域 */
.rating-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.rating-section:last-of-type {
  border-bottom: none;
}

.rating-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.stars {
  display: flex;
  gap: 8rpx;
}

.star-container {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.star {
  font-size: 48rpx;
  color: #ddd;
  cursor: pointer;
}

.star.full {
  position: relative;
  z-index: 1;
}

.star.half {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  overflow: hidden;
  z-index: 2;
}

/* 当半星不激活时，让它透明，这样不会遮挡完整星星 */
.star.half:not(.active) {
  color: transparent;
}

.star.active {
  color: #ffa726;
}

.rating-score {
  font-size: 28rpx;
  color: #666;
  min-width: 60rpx;
  text-align: right;
}

/* 评价内容输入 */
.comment-section {
  margin-top: 32rpx;
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.comment-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  display: block;
  margin-bottom: 16rpx;
}

.comment-input {
  width: 100%;
  min-height: 200rpx;
  padding: 16rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  background: #fafafa;
  box-sizing: border-box;
}

/* 提交按钮 */
.submit-section {
  padding: 32rpx;
}

.submit-btn {
  width: 100%;
  padding: 24rpx 0;
  background: #1e98d7;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
