<template>
  <view class="personal-info-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="nav-back" @click="goBack">＜</text>
      <text class="nav-title">我的信息</text>
      <view class="nav-right">
        <text class="nav-dots">⋯</text>
        <text class="nav-help">?</text>
      </view>
    </view>

    <!-- 认证状态提示 -->
    <view class="auth-status" v-if="authInfo.status === 'verified'">
      <text class="status-text">您已通过认证，以下是您的认证信息</text>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">基本信息</view>
      
      <view class="info-item">
        <text class="info-label">头像</text>
        <view class="avatar-container">
          <image v-if="userInfo.avatar" :src="userInfo.avatar" class="avatar-image" />
          <view v-else class="avatar-placeholder">
            <text class="avatar-text">头像</text>
          </view>
        </view>
      </view>

      <view class="info-item">
        <text class="info-label">真实姓名</text>
        <text class="info-value">{{ userInfo.realName || '未设置' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">手机号</text>
        <text class="info-value">{{ userInfo.phone || '未设置' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">身份证</text>
        <text class="info-value">{{ formatIdCard(userInfo.idCard) }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">毕业学校</text>
        <text class="info-value">{{ userInfo.school || '未设置' }}</text>
      </view>

      <view class="info-item">
        <text class="info-label">学历</text>
        <view class="degree-badge" v-if="userInfo.degree">
          <text class="degree-text">{{ userInfo.degree }}</text>
        </view>
        <text v-else class="info-value">未设置</text>
      </view>

      <view class="info-item">
        <text class="info-label">专业或研究方向</text>
        <text class="info-value">{{ userInfo.major || '未设置' }}</text>
      </view>
    </view>

    <!-- 科研经历 -->
    <view class="info-section">
      <view class="section-title">科研经历</view>
      <view class="experience-content">
        <text class="experience-text">{{ userInfo.researchExperience || '暂无科研经历' }}</text>
      </view>
    </view>

    <!-- 个人自述 -->
    <view class="info-section">
      <view class="section-title">个人自述</view>
      <view class="statement-content">
        <text class="statement-text">{{ userInfo.personalStatement || '暂无个人自述' }}</text>
      </view>
    </view>

    <!-- 个人证件 -->
    <view class="info-section">
      <view class="section-title">个人证件 (个人证件仅作为身份验证，不会对外展示)</view>
      
      <view class="cert-grid">
        <!-- 身份证人像面 -->
        <view class="cert-item">
          <view class="cert-label">身份证人像面</view>
          <view class="cert-image-container">
            <image v-if="userInfo.idCardFront" :src="userInfo.idCardFront" class="cert-image" />
            <view v-else class="cert-placeholder">
              <text class="cert-text">未上传</text>
            </view>
          </view>
        </view>

        <!-- 身份证国徽面 -->
        <view class="cert-item">
          <view class="cert-label">身份证国徽面</view>
          <view class="cert-image-container">
            <image v-if="userInfo.idCardBack" :src="userInfo.idCardBack" class="cert-image" />
            <view v-else class="cert-placeholder">
              <text class="cert-text">未上传</text>
            </view>
          </view>
        </view>

        <!-- 学历证件 -->
        <view class="cert-item">
          <view class="cert-label">学历证件</view>
          <view class="cert-image-container">
            <image v-if="userInfo.educationCert" :src="userInfo.educationCert" class="cert-image" />
            <view v-else class="cert-placeholder">
              <text class="cert-text">未上传</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 修改个人信息按钮 -->
    <view class="action-section">
      <button class="edit-btn" @click="editInfo">修改个人信息</button>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '../../../store/user.js'

const userStore = useUserStore()

// 获取用户信息和认证信息
const userInfo = computed(() => userStore.userInfo)
const authInfo = computed(() => userStore.authInfo)

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 格式化身份证号（隐藏中间部分）
const formatIdCard = (idCard) => {
  if (!idCard) return '未设置'
  if (idCard.length < 8) return idCard
  return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4)
}

// 修改个人信息
const editInfo = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}
</script>

<style scoped>
.personal-info-page {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-dots {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-help {
  font-size: 32rpx;
  color: #333;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 认证状态 */
.auth-status {
  background: #e8f5e8;
  padding: 24rpx 32rpx;
  margin: 20rpx;
  border-radius: 12rpx;
}

.status-text {
  font-size: 28rpx;
  color: #52c41a;
}

/* 信息区块 */
.info-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 32rpx;
}

.info-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #333;
  width: 200rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #666;
  flex: 1;
}

/* 头像 */
.avatar-container {
  flex: 1;
}

.avatar-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
}

.avatar-placeholder {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f0f0f0;
  border: 2rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  font-size: 24rpx;
  color: #999;
}

/* 学历标签 */
.degree-badge {
  background: #1890ff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.degree-text {
  font-size: 24rpx;
  color: #fff;
}

/* 经历和自述 */
.experience-content,
.statement-content {
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  min-height: 120rpx;
}

.experience-text,
.statement-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 证件网格 */
.cert-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.cert-item {
  flex: 1;
  min-width: 200rpx;
}

.cert-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.cert-image-container {
  width: 100%;
  height: 240rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.cert-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cert-placeholder {
  width: 100%;
  height: 100%;
  background: #f0f0f0;
  border: 2rpx dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cert-text {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.action-section {
  padding: 40rpx 32rpx;
}

.edit-btn {
  width: 100%;
  height: 88rpx;
  background: #1890ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-btn:active {
  background: #096dd9;
}
</style>
