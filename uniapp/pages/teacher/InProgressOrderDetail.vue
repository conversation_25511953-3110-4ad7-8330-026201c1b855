<template>
  <view class="order-detail">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="back" @click="goBack">＜</text>
      <text class="nav-title">订单详情</text>
      <view class="nav-actions">
        <text class="nav-icon">⋯</text>
        <text class="nav-icon">⟲</text>
      </view>
    </view>

    <!-- 标签页 -->
    <view class="tab-bar">
      <view class="tab-item" :class="{ active: activeTab === 'basic' }" @click="switchTab('basic')">基本信息</view>
      <view class="tab-item" :class="{ active: activeTab === 'delivery' }" @click="switchTab('delivery')">交付信息</view>
    </view>

    <!-- 订单详情内容 -->
    <scroll-view class="content" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <!-- 基本信息内容 -->
      <view class="basic-info" v-if="activeTab === 'basic'">
        <!-- 基本信息列表 -->
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">订单号</text>
            <text class="info-value">{{ order.no || 'JS13455555333' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">发布时间</text>
            <text class="info-value">{{ order.date || '2025-3-12 12:11:12' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">学生名称</text>
            <text class="info-value">{{ order.studentName || '张菲菲' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">辅导项目</text>
            <text class="info-value">{{ order.type || '作业' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">学习阶段</text>
            <text class="info-value">{{ order.stage || '本科' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">学习位置</text>
            <text class="info-value">{{ order.location || '国内' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">学习专业</text>
            <text class="info-value">{{ order.major || '会计' }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">预算区间</text>
            <text class="info-value budget">{{ order.budget || '1000-2000元' }}</text>
          </view>
        </view>

        <!-- 需求描述 -->
        <view class="description-section">
          <view class="section-title">
            <text class="title-text">需求描述</text>
          </view>
          <text class="description-text">{{ order.description || '想要找一个辅导论文的老师，想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师想要找一个辅导论文的老师' }}</text>
        </view>

        <!-- 老师要求 -->
        <view class="teacher-requirement-section">
          <view class="section-title">
            <text class="title-text">老师要求</text>
          </view>
        </view>

        <!-- 老师情况 -->
        <view class="teacher-status-section">
          <view class="section-title">
            <text class="title-text">老师情况</text>
          </view>
          <view class="teacher-tags">
            <text class="tag-btn active">博士</text>
          </view>
        </view>

        <!-- 是否留学 -->
        <view class="study-abroad-section">
          <view class="section-title">
            <text class="title-text">是否留学</text>
          </view>
          <view class="study-abroad">
            <text class="tag-btn inactive">不要求</text>
          </view>
        </view>

        <!-- 其他说明 -->
        <view class="other-section">
          <text class="other-text">{{ order.extra || '希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心希望老师热情专业，有耐心。' }}</text>
        </view>
      </view>

      <!-- 交付信息内容 -->
      <view class="delivery-info" v-if="activeTab === 'delivery'">
        <!-- 交付阶段表格 -->
        <view class="delivery-table">
          <!-- 表格头部 -->
          <view class="table-header">
            <text class="header-cell stage">交付阶段</text>
            <text class="header-cell amount">阶段金额</text>
            <text class="header-cell status">阶段状态</text>
          </view>

          <!-- 表格内容 -->
          <view class="table-body">
            <view class="table-row" v-for="(stage, index) in deliveryStages" :key="index">
              <text class="table-cell stage">{{ stage.name }}</text>
              <text class="table-cell amount" :class="{ highlight: stage.highlight }">{{ stage.amount }}</text>
              <text class="table-cell status" :class="stage.statusClass">{{ stage.status }}</text>
            </view>
          </view>
        </view>

        <!-- 注释说明 -->
        <view class="notes-section">
          <view class="notes-title">
            <text class="notes-label">注释：</text>
          </view>
          <view class="notes-content">
            <text class="note-item">1、学生支付：学生需预先支付当前阶段费用，状态变为待交付后方可开始作业，应止作业后不付款。</text>
            <text class="note-item">2、验收机制：</text>
            <text class="note-item">老师完成阶段任务后，需与学生线下确认收货；</text>
            <text class="note-item highlight">学生流在平台进行线上验收，状态变更为【已验收】后方可进入下一阶段。</text>
            <text class="note-item">3、提现条件：所有阶段均达【已验收】状态，且学生14天无异议提现后，方可提现。</text>
            <text class="note-item">4、订单终止：需师生协商一致，由学生在平台主动终止订单。</text>
            <text class="note-item">5、争议处理：若产生分歧，双方可申请平台客服介入协调。</text>
          </view>
        </view>

        <!-- 底部提示 -->
        <view class="bottom-notice">
          <text class="notice-text">注意：线下交易有风险，出现问题无人管。</text>
          <text class="notice-text">一旦发现会封号，也不利于积累人气。</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部操作按钮 -->
    <view class="bottom-action">
      <button class="action-btn secondary" @click="updateProgress">更新进度</button>
      <button class="action-btn primary" @click="completeOrder">完成订单</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

const scrollHeight = ref(600)
const progressPercent = ref(65)
const activeTab = ref('basic') // 当前激活的标签页

// 订单信息
const order = ref({
  id: '',
  no: '',
  type: '',
  budget: '',
  studentName: '',
  stage: '',
  major: '',
  startTime: '',
  expectedTime: '',
  description: ''
})

// 交付阶段数据
const deliveryStages = ref([
  {
    name: '第1阶段',
    amount: '1,000',
    status: '已验收',
    statusClass: 'completed'
  },
  {
    name: '第2阶段',
    amount: '2,000',
    status: '已验收',
    statusClass: 'completed'
  },
  {
    name: '第3阶段',
    amount: '3,000',
    status: '待交付',
    statusClass: 'pending'
  },
  {
    name: '第4阶段',
    amount: '4,000',
    status: '已交付待验收',
    statusClass: 'delivered'
  },
  {
    name: '第5阶段',
    amount: '3,000',
    status: '待交付',
    statusClass: 'pending'
  },
  {
    name: '共6阶段',
    amount: '13,000',
    status: '进行中',
    statusClass: 'progress',
    highlight: true
  }
])

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 切换标签页
const switchTab = (tab) => {
  activeTab.value = tab
}

// 联系学生
const contactStudent = () => {
  uni.showToast({
    title: '联系学生功能开发中',
    icon: 'none'
  })
}

// 更新进度
const updateProgress = () => {
  uni.showToast({
    title: '更新进度功能开发中',
    icon: 'none'
  })
}

// 完成订单
const completeOrder = () => {
  uni.showModal({
    title: '确认完成',
    content: '确定要完成这个订单吗？',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '订单已完成',
          icon: 'success'
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    }
  })
}

onLoad((options) => {
  if (options && options.order) {
    const orderData = JSON.parse(decodeURIComponent(options.order))
    order.value = { ...order.value, ...orderData }
  }
})

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 280
})
</script>

<style scoped>
.order-detail {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.back {
  font-size: 36rpx;
  color: #333;
  padding: 0 16rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-actions {
  display: flex;
  gap: 16rpx;
}

.nav-icon {
  font-size: 32rpx;
  color: #666;
  padding: 0 8rpx;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1e98d7;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #1e98d7;
  border-radius: 2rpx;
}

/* 进度状态 */
.progress-status {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.status-text {
  font-size: 28rpx;
  color: #1e98d7;
  font-weight: bold;
  margin-bottom: 16rpx;
  display: block;
}

.progress-bar {
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  margin-bottom: 12rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #1e98d7;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

/* 内容区域 */
.content {
  flex: 1;
}

/* 基本信息页面 */
.basic-info {
  background: #fff;
}

/* 信息列表 */
.info-list {
  padding: 0 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 60rpx;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #333;
  width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 30rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

.info-value.budget {
  color: #333;
}

/* 区块样式 */
.description-section, .teacher-requirement-section, .teacher-status-section, .study-abroad-section, .other-section {
  padding: 32rpx;
  border-bottom: 20rpx solid #f8f8f8;
}

.section-title {
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.description-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 标签按钮 */
.teacher-tags, .study-abroad {
  display: flex;
  justify-content: flex-start;
  margin-top: 16rpx;
}

.tag-btn {
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid #ddd;
  margin-right: 16rpx;
}

.tag-btn.active {
  background: #1e98d7;
  color: #fff;
  border-color: #1e98d7;
}

.tag-btn.inactive {
  background: #f8f8f8;
  color: #666;
  border-color: #ddd;
}

.other-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 注释说明 */
.notes-section {
  margin-bottom: 40rpx;
}

.notes-title {
  margin-bottom: 16rpx;
}

.notes-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.notes-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.note-item {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.note-item.highlight {
  color: #007bff;
  font-weight: bold;
}

/* 底部提示 */
.bottom-notice {
  background: #6c757d;
  padding: 20rpx;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.notice-text {
  font-size: 24rpx;
  color: #fff;
  text-align: center;
  line-height: 1.4;
}

/* 交付信息页面 */
.delivery-info {
  background: #fff;
  padding: 32rpx;
}

/* 交付阶段表格 */
.delivery-table {
  margin-bottom: 40rpx;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-bottom: none;
}

.header-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #dee2e6;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.stage {
  flex: 1.2;
}

.header-cell.amount {
  flex: 1;
}

.header-cell.status {
  flex: 1.3;
}

.table-body {
  border: 1rpx solid #dee2e6;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #dee2e6;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  flex: 1;
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  border-right: 1rpx solid #dee2e6;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.stage {
  flex: 1.2;
}

.table-cell.amount {
  flex: 1;
}

.table-cell.amount.highlight {
  color: #dc3545;
  font-weight: bold;
}

.table-cell.status {
  flex: 1.3;
}

.table-cell.status.completed {
  color: #28a745;
}

.table-cell.status.pending {
  color: #6c757d;
}

.table-cell.status.delivered {
  color: #007bff;
}

.table-cell.status.progress {
  color: #dc3545;
  font-weight: bold;
}





/* 底部操作按钮 */
.bottom-action {
  display: flex;
  gap: 20rpx;
  padding: 20rpx 32rpx;
  background: #fff;
  border-top: 1rpx solid #eee;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  border: none;
}

.action-btn.secondary {
  background: #f8f8f8;
  color: #666;
}

.action-btn.primary {
  background: #1e98d7;
  color: #fff;
}
</style>
