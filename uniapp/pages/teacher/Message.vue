<template>
  <view class="message-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="nav-back" @click="goBack">←</text>
      <text class="nav-title">消息</text>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 消息列表 -->
    <scroll-view class="message-list" scroll-y="true" :style="{ height: scrollHeight + 'px' }">
      <view class="message-item" v-for="(message, index) in messages" :key="index" @click="openChat(message)">
        <image class="avatar" :src="message.avatar" mode="aspectFill"></image>
        <view class="message-content">
          <view class="message-header">
            <text class="sender-name">{{ message.senderName }}</text>
            <text class="message-time">{{ message.time }}</text>
          </view>
          <text class="message-preview">{{ message.lastMessage }}</text>
        </view>
        <view class="unread-badge" v-if="message.unreadCount > 0">
          <text class="unread-count">{{ message.unreadCount }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 底部导航 -->
    <view class="bottom-nav">
      <view class="nav-item" @click="goToPage('home')">
        <text class="nav-icon">🏠</text>
        <text class="nav-text">首页</text>
      </view>
      <view class="nav-item active">
        <text class="nav-icon">📋</text>
        <text class="nav-text">消息</text>
      </view>
      <view class="nav-item" @click="goToPage('profile')">
        <text class="nav-icon">👤</text>
        <text class="nav-text">我的</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const scrollHeight = ref(600)

// 模拟消息数据
const messages = ref([
  {
    id: '1',
    senderName: '张伟',
    avatar: '/static/logo.png',
    lastMessage: '老师您好，关于论文的问题想请教一下',
    time: '10:30',
    unreadCount: 2
  },
  {
    id: '2',
    senderName: '李明',
    avatar: '/static/logo.png',
    lastMessage: '谢谢老师的指导，我已经修改完了',
    time: '昨天',
    unreadCount: 0
  },
  {
    id: '3',
    senderName: '王芳',
    avatar: '/static/logo.png',
    lastMessage: '老师，作业我已经提交了，请查收',
    time: '前天',
    unreadCount: 1
  }
])

const goBack = () => {
  uni.navigateBack()
}

const goToPage = (page) => {
  switch (page) {
    case 'home':
      uni.navigateTo({
        url: '/uniapp/pages/teacher/Home'
      })
      break
    case 'profile':
      uni.navigateTo({
        url: '/uniapp/pages/teacher/Profile'
      })
      break
  }
}

const openChat = (message) => {
  console.log('打开聊天:', message)
  // 这里可以跳转到聊天详情页面
  uni.showToast({
    title: '聊天功能待开发',
    icon: 'none'
  })
}

onMounted(() => {
  const systemInfo = uni.getSystemInfoSync()
  scrollHeight.value = systemInfo.windowHeight - 200
})
</script>

<style scoped>
.message-page {
  min-height: calc(100vh - 140px);
  background: #f8f8f8;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  font-size: 36rpx;
  color: #007aff;
  padding: 0 8rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-placeholder {
  width: 52rpx;
}

/* 消息列表 */
.message-list {
  flex: 1;
  background: #fff;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #f0f0f0;
}

.message-content {
  flex: 1;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.sender-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.message-time {
  font-size: 24rpx;
  color: #999;
}

.message-preview {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.unread-badge {
  position: absolute;
  top: 24rpx;
  right: 32rpx;
  background: #ff4444;
  border-radius: 50%;
  min-width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.unread-count {
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
}

/* 底部导航 */
.bottom-nav {
  display: flex;
  background: #fff;
  border-top: 1rpx solid #eee;
  padding: 16rpx 0;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 0;
}

.nav-item.active .nav-text {
  color: #007aff;
}

.nav-item.active .nav-icon {
  color: #007aff;
}

.nav-icon {
  font-size: 40rpx;
  margin-bottom: 4rpx;
}

.nav-text {
  font-size: 20rpx;
  color: #666;
}
</style>
