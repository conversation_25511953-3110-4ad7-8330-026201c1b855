<template>
  <view class="tutor-skills-page">
    <!-- 顶部导航栏 -->
    <view class="nav-bar">
      <text class="nav-back" @click="goBack">＜</text>
      <text class="nav-title">我擅长辅导</text>
      <view class="nav-right">
        <text class="nav-dots">⋯</text>
        <text class="nav-help">?</text>
      </view>
    </view>

    <!-- 辅导类型列表 -->
    <view class="skills-container">
      <!-- 论文辅导 -->
      <view class="skill-item" @click="selectSkill('论文辅导')">
        <view class="skill-content">
          <text class="skill-title">论文辅导</text>
          <view class="skill-status" v-if="isSkillSelected('论文辅导')">
            <text class="status-text">已选择</text>
          </view>
        </view>
        <view class="skill-tip" v-if="showTip('论文辅导')">
          <text class="tip-text">辅导内容标准化，和用户的需求进行匹配</text>
        </view>
      </view>

      <!-- 专业课辅导 -->
      <view class="skill-item" @click="selectSkill('专业课辅导')">
        <view class="skill-content">
          <text class="skill-title">专业课辅导</text>
          <view class="skill-status" v-if="isSkillSelected('专业课辅导')">
            <text class="status-text">已选择</text>
          </view>
        </view>
      </view>

      <!-- 保研辅导 -->
      <view class="skill-item" @click="selectSkill('保研辅导')">
        <view class="skill-content">
          <text class="skill-title">保研辅导</text>
          <view class="skill-status" v-if="isSkillSelected('保研辅导')">
            <text class="status-text">已选择</text>
          </view>
        </view>
        <view class="skill-tip" v-if="showTip('保研辅导')">
          <text class="tip-text">可辅导内容：论文、专业课、其他具体的可以自由编辑，编辑时可以做出提示</text>
        </view>
      </view>

      <!-- 考研辅导 -->
      <view class="skill-item" @click="selectSkill('考研辅导')">
        <view class="skill-content">
          <text class="skill-title">考研辅导</text>
          <view class="skill-status" v-if="isSkillSelected('考研辅导')">
            <text class="status-text">已选择</text>
          </view>
        </view>
      </view>

      <!-- 其他 -->
      <view class="skill-item" @click="selectSkill('其他')">
        <view class="skill-content">
          <text class="skill-title">其他</text>
          <view class="skill-status" v-if="isSkillSelected('其他')">
            <text class="status-text">已选择</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 已选择的技能展示 -->
    <view class="selected-skills" v-if="selectedSkills.length > 0">
      <view class="section-title">已选择的辅导类型</view>
      <view class="selected-tags">
        <view class="selected-tag" v-for="skill in selectedSkills" :key="skill" @click="removeSkill(skill)">
          <text class="tag-text">{{ skill }}</text>
          <text class="remove-btn">×</text>
        </view>
      </view>
    </view>

    <!-- 详细说明输入 -->
    <view class="description-section">
      <view class="section-title">详细说明（可选）</view>
      <textarea 
        class="description-input"
        v-model="skillDescription"
        placeholder="请详细描述您的辅导专长、经验和能力，这将帮助学生更好地了解您的服务..."
        maxlength="500"
      />
      <view class="char-count">{{ skillDescription.length }}/500</view>
    </view>

    <!-- 保存按钮 -->
    <view class="action-section">
      <button class="save-btn" @click="saveSkills">保存设置</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { useUserStore } from '../../../store/user.js'

const userStore = useUserStore()

// 已选择的技能
const selectedSkills = ref([])

// 技能详细说明
const skillDescription = ref('')

// 显示提示的技能
const tipsVisible = ref({
  '论文辅导': true,
  '保研辅导': true
})

// 初始化数据
const initData = () => {
  // 从用户信息中获取已设置的技能
  const userInfo = userStore.userInfo
  if (userInfo.tutorSkills) {
    selectedSkills.value = [...userInfo.tutorSkills]
  }
  if (userInfo.skillDescription) {
    skillDescription.value = userInfo.skillDescription
  }
}

// 页面加载时初始化
initData()

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 选择/取消选择技能
const selectSkill = (skill) => {
  const index = selectedSkills.value.indexOf(skill)
  if (index > -1) {
    selectedSkills.value.splice(index, 1)
  } else {
    selectedSkills.value.push(skill)
  }
}

// 检查技能是否已选择
const isSkillSelected = (skill) => {
  return selectedSkills.value.includes(skill)
}

// 显示提示
const showTip = (skill) => {
  return tipsVisible.value[skill] && isSkillSelected(skill)
}

// 移除已选择的技能
const removeSkill = (skill) => {
  const index = selectedSkills.value.indexOf(skill)
  if (index > -1) {
    selectedSkills.value.splice(index, 1)
  }
}

// 保存技能设置
const saveSkills = () => {
  if (selectedSkills.value.length === 0) {
    uni.showToast({
      title: '请至少选择一项辅导技能',
      icon: 'none'
    })
    return
  }

  // 更新用户信息
  userStore.setUserInfo({
    tutorSkills: [...selectedSkills.value],
    skillDescription: skillDescription.value
  })

  uni.showToast({
    title: '保存成功',
    icon: 'success'
  })

  // 延迟返回上一页
  setTimeout(() => {
    uni.navigateBack()
  }, 1500)
}
</script>

<style scoped>
.tutor-skills-page {
  min-height: calc(100vh - 140px);
  background: #f5f5f5;
}

/* 顶部导航栏 */
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}

.nav-back {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  text-align: center;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.nav-dots {
  font-size: 32rpx;
  color: #333;
  padding: 0 8rpx;
}

.nav-help {
  font-size: 32rpx;
  color: #333;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #333;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 技能容器 */
.skills-container {
  padding: 20rpx;
}

.skill-item {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.skill-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.skill-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.skill-status {
  background: #52c41a;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.status-text {
  font-size: 24rpx;
  color: #fff;
}

.skill-tip {
  padding: 24rpx 32rpx;
  background: #fff9c4;
}

.tip-text {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.5;
}

/* 已选择的技能 */
.selected-skills {
  margin: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.selected-tag {
  display: flex;
  align-items: center;
  background: #e6f7ff;
  border: 1rpx solid #91d5ff;
  border-radius: 20rpx;
  padding: 12rpx 20rpx;
}

.tag-text {
  font-size: 26rpx;
  color: #1890ff;
  margin-right: 8rpx;
}

.remove-btn {
  font-size: 28rpx;
  color: #1890ff;
  font-weight: bold;
}

/* 详细说明 */
.description-section {
  margin: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
}

.description-input {
  width: 100%;
  min-height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-top: 16rpx;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 12rpx;
}

/* 操作按钮 */
.action-section {
  padding: 40rpx 32rpx;
}

.save-btn {
  width: 100%;
  height: 88rpx;
  background: #1890ff;
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn:active {
  background: #096dd9;
}
</style>
