const fs = require('fs');
const path = require('path');

// 创建一个简单的SVG到PNG转换器
// 由于没有安装专门的转换工具，我们直接创建优化的PNG数据

const createPNGFromSVG = (svgPath, pngPath) => {
  const svgContent = fs.readFileSync(svgPath, 'utf8');
  
  // 创建一个简单的48x48像素的PNG文件头
  // 这是一个基本的PNG文件结构
  const width = 48;
  const height = 48;
  
  // 由于直接创建PNG比较复杂，我们先创建一个HTML文件来帮助转换
  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SVG to PNG Converter</title>
</head>
<body>
    <div id="svg-container">${svgContent}</div>
    <canvas id="canvas" width="48" height="48" style="border: 1px solid #ccc;"></canvas>
    
    <script>
        const svg = document.querySelector('#svg-container svg');
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // 创建一个图像对象
        const img = new Image();
        const svgBlob = new Blob([new XMLSerializer().serializeToString(svg)], {type: 'image/svg+xml'});
        const url = URL.createObjectURL(svgBlob);
        
        img.onload = function() {
            ctx.drawImage(img, 0, 0, 48, 48);
            
            // 转换为PNG并下载
            canvas.toBlob(function(blob) {
                const link = document.createElement('a');
                link.download = '${path.basename(pngPath)}';
                link.href = URL.createObjectURL(blob);
                link.click();
            }, 'image/png');
            
            URL.revokeObjectURL(url);
        };
        
        img.src = url;
    </script>
</body>
</html>`;
  
  // 保存HTML文件
  const htmlPath = pngPath.replace('.png', '.html');
  fs.writeFileSync(htmlPath, htmlContent);
  
  console.log(`Created HTML converter: ${htmlPath}`);
  console.log(`Please open this file in a browser to download the PNG: ${path.basename(pngPath)}`);
};

// 转换所有SVG文件
const svgFiles = [
  'static/home.svg',
  'static/home_selected.svg', 
  'static/mine.svg',
  'static/mine_selected.svg'
];

svgFiles.forEach(svgFile => {
  const pngFile = svgFile.replace('.svg', '.png');
  createPNGFromSVG(svgFile, pngFile);
});

console.log('SVG to PNG conversion setup complete!');
console.log('Please open the generated HTML files in your browser to download the PNG files.');
