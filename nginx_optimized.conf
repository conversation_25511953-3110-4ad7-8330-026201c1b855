# HTTP服务器 - 重定向到HTTPS
server {
    listen 80 default_server;
    listen [::]:80 default_server;
    server_name shengmipark.com www.shengmipark.com;
    
    # 重定向所有HTTP请求到HTTPS
    return 301 https://$host$request_uri;
}

# HTTPS服务器
server {
    listen 443 ssl http2;
    server_name shengmipark.com www.shengmipark.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/ssl/shengmipark.com.pem;
    ssl_certificate_key /etc/nginx/ssl/shengmipark.com.key;
    
    # SSL安全配置
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头部
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
  
    # API接口代理
    location /api/ {
        proxy_pass http://localhost:3000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 10s;
        proxy_read_timeout 10s;
    }
    
    # 根路径显示API信息
    location / {
        return 200 '{"message":"停车管理系统API服务运行中","status":"online","api_endpoint":"/api/","health_check":"/health"}';
        add_header Content-Type application/json;
    }
    
    # 上传文件访问
    location /uploads/ {
        root /opt/ParkStationSystem/server;
        expires 30d;
        add_header Cache-Control "public, immutable";
        
        # 安全设置 - 防止执行脚本文件
        location ~* \.(php|jsp|asp|sh|pl|py)$ {
            deny all;
        }
    }
}
